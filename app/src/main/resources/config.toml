# Tagify Configuration File

[general]
# Application name
name = "Tagify"
# Application version
version = "1.0.0"
# Default language
language = "en"
# Debug mode
debug = false
# Verbose logging
verbose = false

[paths]
# Directory for configuration files
config_dir = "${user.home}/.tagify/config"
# Directory for plugin files
plugin_dir = "${user.home}/.tagify/plugins"
# Directory for language files
lang_dir = "${user.home}/.tagify/lang"
# Directory for data files
data_dir = "${user.home}/.tagify/data"

[database]
# Database type (sqlite, mysql, postgresql)
type = "sqlite"
# Database file path (for sqlite)
path = "${paths.data_dir}/tagify.db"
# Database connection string (for mysql, postgresql)
connection_string = ""
# Database username
username = ""
# Database password
password = ""

[plugins]
# Enable plugins
enabled = true
# Auto-load plugins
auto_load = true
# Plugin blacklist (comma-separated list of plugin IDs)
blacklist = ""
# Plugin whitelist (comma-separated list of plugin IDs, empty means all)
whitelist = ""

[cache]
# Enable caching
enabled = true
# Maximum cache size (number of entries)
max_size = 10000
# Cache expiration time in seconds (0 means no expiration)
expiration = 3600
# Cache cleanup interval in seconds
cleanup_interval = 300

[ui]
# Color theme (light, dark, system)
theme = "system"
# Enable animations
animations = true
# Font size
font_size = 12
# Date format
date_format = "yyyy-MM-dd"
# Time format
time_format = "HH:mm:ss"

[tags]
# Default tag colors
default_colors = ["#FF5733", "#33FF57", "#3357FF", "#F3FF33", "#FF33F3", "#33FFF3"]
# Maximum tag name length
max_name_length = 50
# Maximum tag description length
max_description_length = 200
# Allow hierarchical tags
hierarchical = true
# Maximum hierarchy depth (0 means unlimited)
max_hierarchy_depth = 5

[files]
# File extensions to ignore (comma-separated list)
ignore_extensions = ".tmp,.bak,.swp,.~"
# Directories to ignore (comma-separated list)
ignore_directories = ".git,.svn,node_modules,build,dist"
# Maximum file size to process in MB
max_file_size = 100
# Enable file content indexing
index_content = false
# File content indexing extensions (comma-separated list, empty means all)
index_extensions = ".txt,.md,.html,.xml,.json,.csv"