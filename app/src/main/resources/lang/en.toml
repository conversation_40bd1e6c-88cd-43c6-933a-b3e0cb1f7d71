# English language file for Tagify

[app]
name = "Tagify"
description = "A modular, extensible, cross-platform file tagging and search tool"
version = "Version {0}"
copyright = "© 2025 Mystery2099. All rights reserved."
welcome = "Welcome to Tagify!"
goodbye = "Thank you for using Tagify. Goodbye!"

[common]
yes = "Yes"
no = "No"
ok = "OK"
cancel = "Cancel"
save = "Save"
delete = "Delete"
edit = "Edit"
add = "Add"
remove = "Remove"
search = "Search"
filter = "Filter"
sort = "Sort"
open = "Open"
close = "Close"
back = "Back"
next = "Next"
previous = "Previous"
finish = "Finish"
help = "Help"
settings = "Settings"
error = "Error"
warning = "Warning"
info = "Information"
success = "Success"
loading = "Loading..."
processing = "Processing..."
not_found = "Not found"
unknown = "Unknown"
none = "None"
all = "All"
selected = "Selected"
name = "Name"
description = "Description"
path = "Path"
date = "Date"
size = "Size"
type = "Type"
created = "Created"
modified = "Modified"
accessed = "Accessed"

[errors]
general = "An error occurred: {0}"
file_not_found = "File not found: {0}"
directory_not_found = "Directory not found: {0}"
permission_denied = "Permission denied: {0}"
invalid_argument = "Invalid argument: {0}"
invalid_operation = "Invalid operation: {0}"
not_implemented = "This feature is not implemented yet"
plugin_load_failed = "Failed to load plugin: {0}"
plugin_not_found = "Plugin not found: {0}"
config_load_failed = "Failed to load configuration: {0}"
lang_load_failed = "Failed to load language file: {0}"
database_error = "Database error: {0}"
tag_not_found = "Tag not found: {0}"
file_too_large = "File is too large: {0}"
unsupported_file_type = "Unsupported file type: {0}"

[commands]
help_usage = "Usage: {0}"
help_description = "Description: {0}"
help_examples = "Examples:"
help_available_commands = "Available commands:"
help_available_options = "Available options:"
help_deprecated = "This command is deprecated: {0}"

[commands.add]
name = "add"
description = "Add tags to files"
usage = "add [options] <file> <tag>..."
example1 = "add document.pdf important work"
example2 = "add --recursive ./photos vacation summer"
success = "Added {0} tag(s) to {1} file(s)"
option_recursive = "Apply tags to all files in directories recursively"
option_force = "Force adding tags even if the file is already tagged"
option_dry_run = "Show what would be done without actually doing it"

[commands.remove]
name = "remove"
description = "Remove tags from files"
usage = "remove [options] <file> <tag>..."
example1 = "remove document.pdf important"
example2 = "remove --recursive ./photos vacation"
success = "Removed {0} tag(s) from {1} file(s)"
option_recursive = "Remove tags from all files in directories recursively"
option_all = "Remove all tags from the specified files"
option_dry_run = "Show what would be done without actually doing it"

[commands.list]
name = "list"
description = "List files with their tags"
usage = "list [options] [directory]"
example1 = "list"
example2 = "list --recursive ./documents"
example3 = "list --tag important"
no_files = "No files found"
option_recursive = "List files in directories recursively"
option_tag = "Filter files by tag"
option_format = "Output format (text, json, csv)"
option_sort = "Sort by (name, path, date, size)"
option_reverse = "Reverse sort order"

[commands.tags]
name = "tags"
description = "Manage tags"
usage = "tags [options] [command]"
example1 = "tags list"
example2 = "tags create work --color #FF5733"
example3 = "tags delete old-tag"
no_tags = "No tags found"
option_color = "Tag color (hex format: #RRGGBB)"
option_parent = "Parent tag for hierarchical organization"

[commands.tags.list]
name = "list"
description = "List all tags"
usage = "tags list [options]"

[commands.tags.create]
name = "create"
description = "Create a new tag"
usage = "tags create <name> [options]"
success = "Tag '{0}' created successfully"
already_exists = "Tag '{0}' already exists"

[commands.tags.delete]
name = "delete"
description = "Delete a tag"
usage = "tags delete <name> [options]"
success = "Tag '{0}' deleted successfully"
confirm = "Are you sure you want to delete tag '{0}'? This will remove it from all files."

[commands.tags.rename]
name = "rename"
description = "Rename a tag"
usage = "tags rename <old-name> <new-name>"
success = "Tag '{0}' renamed to '{1}'"

[commands.tags.update]
name = "update"
description = "Update tag properties"
usage = "tags update <name> [options]"
success = "Tag '{0}' updated successfully"

[commands.open]
name = "open"
description = "Open files with the default application"
usage = "open [options] <file>..."
example1 = "open document.pdf"
example2 = "open --tag important"
success = "Opened {0} file(s)"
option_tag = "Open all files with the specified tag"

[commands.plugins]
name = "plugins"
description = "Manage plugins"
usage = "plugins [options] [command]"
example1 = "plugins list"
example2 = "plugins install ./my-plugin.jar"
example3 = "plugins uninstall my-plugin"
no_plugins = "No plugins found"

[commands.plugins.list]
name = "list"
description = "List all plugins"
usage = "plugins list [options]"

[commands.plugins.install]
name = "install"
description = "Install a plugin"
usage = "plugins install <plugin-path>"
success = "Plugin '{0}' installed successfully"
already_installed = "Plugin '{0}' is already installed"

[commands.plugins.uninstall]
name = "uninstall"
description = "Uninstall a plugin"
usage = "plugins uninstall <plugin-id>"
success = "Plugin '{0}' uninstalled successfully"
confirm = "Are you sure you want to uninstall plugin '{0}'?"

[commands.plugins.enable]
name = "enable"
description = "Enable a plugin"
usage = "plugins enable <plugin-id>"
success = "Plugin '{0}' enabled successfully"
already_enabled = "Plugin '{0}' is already enabled"

[commands.plugins.disable]
name = "disable"
description = "Disable a plugin"
usage = "plugins disable <plugin-id>"
success = "Plugin '{0}' disabled successfully"
already_disabled = "Plugin '{0}' is already disabled"

[commands.config]
name = "config"
description = "Manage configuration"
usage = "config [options] [command]"
example1 = "config get general.language"
example2 = "config set general.language fr"
example3 = "config list"

[commands.config.get]
name = "get"
description = "Get a configuration value"
usage = "config get <key>"
not_found = "Configuration key '{0}' not found"

[commands.config.set]
name = "set"
description = "Set a configuration value"
usage = "config set <key> <value>"
success = "Configuration key '{0}' set to '{1}'"

[commands.config.list]
name = "list"
description = "List all configuration values"
usage = "config list [options]"
option_section = "List only values in the specified section"

[commands.config.reset]
name = "reset"
description = "Reset configuration to default values"
usage = "config reset [options]"
success = "Configuration reset to default values"
confirm = "Are you sure you want to reset the configuration to default values?"
option_key = "Reset only the specified key"