package com.github.mystery2099.tagify.app.cli

import com.github.mystery2099.tagify.core.cli.Command
import com.github.mystery2099.tagify.core.cli.CommandContext
import com.github.mystery2099.tagify.core.cli.CommandHandler
import com.github.mystery2099.tagify.core.cli.CommandResult
import com.github.mystery2099.tagify.core.config.ConfigProvider
import com.github.mystery2099.tagify.core.i18n.LanguageProvider
import com.github.mystery2099.tagify.core.plugin.PluginHandler
import com.github.mystery2099.tagify.core.plugin.PluginRegistry
import org.slf4j.LoggerFactory
import java.io.BufferedReader
import java.io.PrintWriter
import java.nio.file.Path
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap

/**
 * Default implementation of the CommandHandler interface.
 *
 * This class manages the registration, finding, and execution of commands in the Tagify system.
 *
 * @property configProvider The configuration provider
 * @property languageProvider The language provider
 * @property pluginHandler The plugin handler
 * @property pluginRegistry The plugin registry
 */
class DefaultCommandHandler(
    private val configProvider: ConfigProvider,
    private val languageProvider: LanguageProvider,
    private val pluginHandler: PluginHandler,
    private val pluginRegistry: PluginRegistry
) : CommandHandler {
    private val logger = LoggerFactory.getLogger(DefaultCommandHandler::class.java)
    private val commands = ConcurrentHashMap<String, Command>()
    private val aliases = ConcurrentHashMap<String, String>()
    
    override fun registerCommand(command: Command) {
        val name = command.getName()
        if (commands.containsKey(name)) {
            throw IllegalArgumentException("Command with name '$name' is already registered")
        }
        
        commands[name] = command
        
        // Register aliases
        for (alias in command.getAliases()) {
            if (aliases.containsKey(alias)) {
                logger.warn("Alias '$alias' for command '$name' is already registered for command '${aliases[alias]}', ignoring")
            } else {
                aliases[alias] = name
            }
        }
        
        // Register subcommands
        for (subcommand in command.getSubcommands()) {
            registerCommand(subcommand)
        }
        
        logger.debug("Registered command: $name")
    }
    
    override fun unregisterCommand(commandName: String): Boolean {
        val command = commands.remove(commandName)
        if (command != null) {
            // Remove aliases
            aliases.entries.removeIf { it.value == commandName }
            
            // Unregister subcommands
            for (subcommand in command.getSubcommands()) {
                unregisterCommand(subcommand.getName())
            }
            
            logger.debug("Unregistered command: $commandName")
            return true
        }
        return false
    }
    
    override fun findCommand(commandName: String): Command? {
        // Check if the command name is registered directly
        val command = commands[commandName]
        if (command != null) {
            return command
        }
        
        // Check if the command name is an alias
        val aliasedName = aliases[commandName]
        if (aliasedName != null) {
            return commands[aliasedName]
        }
        
        return null
    }
    
    override fun getAllCommands(): List<Command> {
        return commands.values.toList()
    }
    
    override fun getTopLevelCommands(): List<Command> {
        return commands.values.filter { it.getParent() == null }
    }
    
    override suspend fun executeCommand(commandName: String, args: List<String>, context: CommandContext): CommandResult {
        val command = findCommand(commandName)
        if (command == null) {
            logger.warn("Command not found: $commandName")
            return CommandResult.commandNotFound(commandName)
        }
        
        if (command.isDeprecated()) {
            val message = command.getDeprecationMessage() ?: "This command is deprecated"
            logger.warn("Executing deprecated command: $commandName - $message")
            context.printError("Warning: $message")
        }
        
        logger.debug("Executing command: $commandName with args: $args")
        return try {
            command.execute(args, context)
        } catch (e: IllegalArgumentException) {
            logger.warn("Invalid arguments for command: $commandName", e)
            CommandResult.invalidArguments(e.message ?: "Invalid arguments")
        } catch (e: Exception) {
            logger.error("Error executing command: $commandName", e)
            CommandResult.internalError("Error executing command: ${e.message}", e)
        }
    }
    
    override suspend fun parseAndExecute(commandLine: String, context: CommandContext): CommandResult {
        if (commandLine.isBlank()) {
            return CommandResult.invalidArguments("Empty command line")
        }
        
        val parts = commandLine.split(Regex("\\s+"))
        val commandName = parts[0]
        val args = parts.subList(1, parts.size)
        
        return executeCommand(commandName, args, context)
    }
    
    override fun getCommandHelp(commandName: String): String? {
        val command = findCommand(commandName)
        return command?.getHelp()
    }
    
    override fun getCommandExamples(commandName: String): List<String> {
        val command = findCommand(commandName)
        return command?.getExamples() ?: emptyList()
    }
    
    override fun getSubcommands(commandName: String): List<Command> {
        val command = findCommand(commandName)
        return command?.getSubcommands() ?: emptyList()
    }
    
    override fun isCommandDeprecated(commandName: String): Boolean {
        val command = findCommand(commandName)
        return command?.isDeprecated() ?: false
    }
    
    override fun getCommandDeprecationMessage(commandName: String): String? {
        val command = findCommand(commandName)
        return if (command?.isDeprecated() == true) {
            command.getDeprecationMessage()
        } else {
            null
        }
    }
    
    /**
     * Creates a command context for executing commands.
     *
     * @param input The input reader
     * @param output The output writer
     * @param error The error writer
     * @param locale The locale
     * @param workingDirectory The working directory
     * @return The command context
     */
    fun createContext(
        input: BufferedReader = System.`in`.bufferedReader(),
        output: PrintWriter = PrintWriter(System.out, true),
        error: PrintWriter = PrintWriter(System.err, true),
        locale: Locale = Locale.getDefault(),
        workingDirectory: Path = Path.of("").toAbsolutePath()
    ): CommandContext {
        return CommandContext(
            input = input,
            output = output,
            error = error,
            configProvider = configProvider,
            languageProvider = languageProvider,
            tagManager = pluginRegistry.getFirstExtension(com.github.mystery2099.tagify.core.api.TagManager::class)
                ?: throw IllegalStateException("No TagManager extension found"),
            fileTracker = pluginRegistry.getFirstExtension(com.github.mystery2099.tagify.core.api.FileTracker::class)
                ?: throw IllegalStateException("No FileTracker extension found"),
            pluginHandler = pluginHandler,
            locale = locale,
            workingDirectory = workingDirectory
        )
    }
}