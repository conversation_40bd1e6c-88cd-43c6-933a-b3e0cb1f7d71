package com.github.mystery2099.tagify.app.config

import com.github.mystery2099.tagify.core.config.ConfigProvider
import com.sksamuel.hoplite.ConfigLoader
import com.sksamuel.hoplite.ConfigLoaderBuilder
import com.sksamuel.hoplite.ConfigResult
import com.sksamuel.hoplite.Node
import com.sksamuel.hoplite.PropertySource
import com.sksamuel.hoplite.sources.InputStreamPropertySource
import com.sksamuel.hoplite.toml.TomlParser
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.slf4j.LoggerFactory
import java.io.FileInputStream
import java.nio.file.Path
import kotlin.io.path.absolutePathString
import kotlin.io.path.exists
import kotlin.io.path.isDirectory
import kotlin.io.path.writeText
import kotlin.reflect.KClass

/**
 * Implementation of ConfigProvider that uses TOML files for configuration.
 *
 * This class uses the HopLite library to load and parse TOML configuration files.
 */
class TomlConfigProvider : ConfigProvider {
    private val logger = LoggerFactory.getLogger(TomlConfigProvider::class.java)
    private var configDirectory: Path? = null
    private var lastLoadedPath: Path? = null
    private var configNode: Node? = null
    
    private val configLoader: ConfigLoader = ConfigLoaderBuilder.default()
        .addFileExtensionMapping("toml", TomlParser())
        .build()
    
    override fun getFormatName(): String = "TOML"
    
    override fun getSupportedExtensions(): List<String> = listOf("toml")
    
    override suspend fun initialize(configDirectory: Path) {
        if (!configDirectory.exists()) {
            withContext(Dispatchers.IO) {
                configDirectory.toFile().mkdirs()
            }
        }
        
        if (!configDirectory.isDirectory()) {
            throw IllegalArgumentException("Config directory is not a directory: $configDirectory")
        }
        
        this.configDirectory = configDirectory
        logger.info("Initialized TomlConfigProvider with config directory: $configDirectory")
    }
    
    override suspend fun loadConfig(path: Path) {
        if (!path.exists()) {
            throw IllegalArgumentException("Config file does not exist: $path")
        }
        
        logger.info("Loading config from: $path")
        
        val propertySource = withContext(Dispatchers.IO) {
            InputStreamPropertySource(FileInputStream(path.toFile()), path.absolutePathString())
        }
        
        val result = configLoader.loadNodeOrThrow(listOf(propertySource))
        configNode = result
        lastLoadedPath = path
        
        logger.debug("Loaded config: $configNode")
    }
    
    override suspend fun loadConfig(name: String) {
        val configDir = configDirectory
            ?: throw IllegalStateException("Config directory not initialized")
        
        val path = configDir.resolve("$name.toml")
        if (!path.exists()) {
            logger.warn("Config file does not exist: $path, creating empty config")
            withContext(Dispatchers.IO) {
                path.writeText("# $name configuration\n")
            }
        }
        
        loadConfig(path)
    }
    
    override fun <T : Any> getConfig(key: String, type: KClass<T>, default: T?): T? {
        val node = configNode ?: return default
        
        return try {
            val result = node.getValueAt(key, type.java)
            when (result) {
                is ConfigResult.Success -> result.value
                is ConfigResult.Failure -> {
                    logger.warn("Failed to get config value for key: $key, error: ${result.error}")
                    default
                }
            }
        } catch (e: Exception) {
            logger.warn("Error getting config value for key: $key", e)
            default
        }
    }
    
    override fun <T : Any> getConfigSection(key: String, type: KClass<T>): T? {
        val node = configNode ?: return null
        
        return try {
            val result = node.getValueAt(key, type.java)
            when (result) {
                is ConfigResult.Success -> result.value
                is ConfigResult.Failure -> {
                    logger.warn("Failed to get config section for key: $key, error: ${result.error}")
                    null
                }
            }
        } catch (e: Exception) {
            logger.warn("Error getting config section for key: $key", e)
            null
        }
    }
    
    override fun getConfigKeys(): List<String> {
        val node = configNode ?: return emptyList()
        return node.getKeys()
    }
    
    override fun hasConfig(key: String): Boolean {
        val node = configNode ?: return false
        return node.hasPath(key)
    }
    
    override suspend fun setConfig(key: String, value: Any) {
        // HopLite doesn't support modifying config directly, so we need to
        // create a new config with the updated value and save it
        
        // This is a simplified implementation that doesn't handle nested keys
        // A more complete implementation would need to handle nested keys and
        // preserve the structure of the existing config
        
        val path = lastLoadedPath
            ?: throw IllegalStateException("No config file has been loaded")
        
        val config = mutableMapOf<String, Any>()
        
        // Copy existing config
        val node = configNode
        if (node != null) {
            for (existingKey in node.getKeys()) {
                val existingValue = getConfig(existingKey, Any::class, null)
                if (existingValue != null) {
                    config[existingKey] = existingValue
                }
            }
        }
        
        // Update or add the new value
        config[key] = value
        
        // Convert to TOML and save
        val toml = buildString {
            for ((k, v) in config) {
                append("$k = ")
                when (v) {
                    is String -> append("\"$v\"")
                    is Number, is Boolean -> append(v)
                    is List<*> -> {
                        append("[")
                        v.forEachIndexed { index, item ->
                            when (item) {
                                is String -> append("\"$item\"")
                                is Number, is Boolean -> append(item)
                                else -> append(item.toString())
                            }
                            if (index < v.size - 1) {
                                append(", ")
                            }
                        }
                        append("]")
                    }
                    else -> append(v.toString())
                }
                append("\n")
            }
        }
        
        withContext(Dispatchers.IO) {
            path.writeText(toml)
        }
        
        // Reload the config
        loadConfig(path)
    }
    
    override suspend fun saveConfig(path: Path) {
        // Similar to setConfig, but saves to a specific path
        val node = configNode
            ?: throw IllegalStateException("No config has been loaded")
        
        val config = mutableMapOf<String, Any>()
        
        // Copy existing config
        for (key in node.getKeys()) {
            val value = getConfig(key, Any::class, null)
            if (value != null) {
                config[key] = value
            }
        }
        
        // Convert to TOML and save
        val toml = buildString {
            for ((k, v) in config) {
                append("$k = ")
                when (v) {
                    is String -> append("\"$v\"")
                    is Number, is Boolean -> append(v)
                    is List<*> -> {
                        append("[")
                        v.forEachIndexed { index, item ->
                            when (item) {
                                is String -> append("\"$item\"")
                                is Number, is Boolean -> append(item)
                                else -> append(item.toString())
                            }
                            if (index < v.size - 1) {
                                append(", ")
                            }
                        }
                        append("]")
                    }
                    else -> append(v.toString())
                }
                append("\n")
            }
        }
        
        withContext(Dispatchers.IO) {
            path.writeText(toml)
        }
        
        logger.info("Saved config to: $path")
    }
    
    override suspend fun saveConfig(name: String) {
        val configDir = configDirectory
            ?: throw IllegalStateException("Config directory not initialized")
        
        val path = configDir.resolve("$name.toml")
        saveConfig(path)
    }
    
    override suspend fun reloadConfig() {
        val path = lastLoadedPath
            ?: throw IllegalStateException("No config file has been loaded")
        
        loadConfig(path)
    }
    
    override fun getConfigPath(): Path? = lastLoadedPath
    
    override fun getConfigDirectory(): Path = configDirectory
        ?: throw IllegalStateException("Config directory not initialized")
}