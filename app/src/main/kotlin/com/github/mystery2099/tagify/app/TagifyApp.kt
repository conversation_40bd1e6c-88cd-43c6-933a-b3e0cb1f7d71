package com.github.mystery2099.tagify.app

import com.github.ajalt.clikt.core.CliktCommand
import com.github.ajalt.clikt.core.subcommands
import com.github.ajalt.clikt.parameters.options.default
import com.github.ajalt.clikt.parameters.options.flag
import com.github.ajalt.clikt.parameters.options.option
import com.github.ajalt.clikt.parameters.types.path
import com.github.mystery2099.tagify.app.commands.*
import com.github.mystery2099.tagify.app.config.TomlConfigProvider
import com.github.mystery2099.tagify.app.i18n.TomlLanguageProvider
import com.github.mystery2099.tagify.app.plugin.DefaultPluginHandler
import com.github.mystery2099.tagify.app.plugin.DefaultPluginRegistry
import com.github.mystery2099.tagify.app.cache.CaffeineCache
import com.github.mystery2099.tagify.core.cache.CacheManager
import com.github.mystery2099.tagify.core.cli.CommandHandler
import com.github.mystery2099.tagify.core.config.ConfigProvider
import com.github.mystery2099.tagify.core.i18n.LanguageProvider
import com.github.mystery2099.tagify.core.plugin.PluginHandler
import com.github.mystery2099.tagify.core.plugin.PluginRegistry
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import java.nio.file.Path
import kotlin.io.path.createDirectories
import kotlin.io.path.exists
import kotlin.system.exitProcess

/**
 * Main entry point for the Tagify application.
 */
class TagifyApp : CliktCommand(
    name = "tagify",
    help = "A modular, extensible, cross-platform file tagging and search tool"
) {
    private val logger = LoggerFactory.getLogger(TagifyApp::class.java)
    
    private val configDir by option(
        "--config-dir", "-c",
        help = "Directory containing configuration files"
    ).path(canBeFile = false).default(Path.of(System.getProperty("user.home"), ".tagify", "config"))
    
    private val pluginDir by option(
        "--plugin-dir", "-p",
        help = "Directory containing plugins"
    ).path(canBeFile = false).default(Path.of(System.getProperty("user.home"), ".tagify", "plugins"))
    
    private val langDir by option(
        "--lang-dir", "-l",
        help = "Directory containing language files"
    ).path(canBeFile = false).default(Path.of(System.getProperty("user.home"), ".tagify", "lang"))
    
    private val dataDir by option(
        "--data-dir", "-d",
        help = "Directory containing data files"
    ).path(canBeFile = false).default(Path.of(System.getProperty("user.home"), ".tagify", "data"))
    
    private val verbose by option(
        "--verbose", "-v",
        help = "Enable verbose logging"
    ).flag(default = false)
    
    private val debug by option(
        "--debug",
        help = "Enable debug mode"
    ).flag(default = false)
    
    private lateinit var configProvider: ConfigProvider
    private lateinit var languageProvider: LanguageProvider
    private lateinit var pluginHandler: PluginHandler
    private lateinit var pluginRegistry: PluginRegistry
    private lateinit var cacheManager: CacheManager
    private lateinit var commandHandler: CommandHandler
    
    override fun run() {
        try {
            // Set up logging
            setupLogging()
            
            // Create directories if they don't exist
            createDirectories()
            
            // Initialize services
            initializeServices()
            
            // Load plugins
            loadPlugins()
            
            // Register commands
            registerCommands()
            
            // If no subcommand was invoked, print help
            if (currentContext.invokedSubcommand == null) {
                echo(currentContext.command.getFormattedHelp())
            }
        } catch (e: Exception) {
            logger.error("Error initializing Tagify", e)
            echo("Error initializing Tagify: ${e.message}", err = true)
            exitProcess(1)
        }
    }
    
    private fun setupLogging() {
        // Configure logging based on verbose and debug flags
        // This would typically be done using a logging configuration file
        // but for simplicity, we'll just log a message here
        logger.info("Starting Tagify")
        if (verbose) {
            logger.info("Verbose logging enabled")
        }
        if (debug) {
            logger.info("Debug mode enabled")
        }
    }
    
    private fun createDirectories() {
        // Create directories if they don't exist
        if (!configDir.exists()) {
            logger.info("Creating config directory: $configDir")
            configDir.createDirectories()
        }
        
        if (!pluginDir.exists()) {
            logger.info("Creating plugin directory: $pluginDir")
            pluginDir.createDirectories()
        }
        
        if (!langDir.exists()) {
            logger.info("Creating language directory: $langDir")
            langDir.createDirectories()
        }
        
        if (!dataDir.exists()) {
            logger.info("Creating data directory: $dataDir")
            dataDir.createDirectories()
        }
    }
    
    private fun initializeServices() {
        // Initialize configuration provider
        configProvider = TomlConfigProvider()
        runBlocking {
            configProvider.initialize(configDir)
            configProvider.loadConfig("config")
        }
        
        // Initialize language provider
        languageProvider = TomlLanguageProvider()
        runBlocking {
            languageProvider.initialize(langDir)
            languageProvider.loadLanguage("en", java.util.Locale.ENGLISH)
        }
        
        // Initialize plugin registry
        pluginRegistry = DefaultPluginRegistry()
        
        // Initialize plugin handler
        pluginHandler = DefaultPluginHandler(pluginRegistry)
        runBlocking {
            pluginHandler.initialize(pluginDir)
        }
        
        // Initialize cache manager
        cacheManager = CaffeineCache()
        
        // Initialize command handler
        commandHandler = DefaultCommandHandler(
            configProvider,
            languageProvider,
            pluginHandler,
            pluginRegistry
        )
    }
    
    private fun loadPlugins() {
        // Load plugins
        runBlocking {
            val loadedPlugins = pluginHandler.loadPlugins()
            logger.info("Loaded ${loadedPlugins.size} plugins: $loadedPlugins")
        }
    }
    
    private fun registerCommands() {
        // Register commands
        subcommands(
            AddCommand(commandHandler),
            RemoveCommand(commandHandler),
            ListCommand(commandHandler),
            TagsCommand(commandHandler),
            OpenCommand(commandHandler),
            PluginsCommand(commandHandler),
            ConfigCommand(commandHandler)
        )
    }
}

/**
 * Main entry point for the application.
 */
fun main(args: Array<String>) {
    TagifyApp().main(args)
}