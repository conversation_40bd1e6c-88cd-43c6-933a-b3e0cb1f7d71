plugins {
    // Apply the shared build logic from a convention plugin.
    // The shared code is located in `buildSrc/src/main/kotlin/kotlin-jvm.gradle.kts`.
    id("buildsrc.convention.kotlin-jvm")
    
    // Apply Kotlin Serialization plugin from `gradle/libs.versions.toml`.
    alias(libs.plugins.kotlinPluginSerialization)

    // Apply the Application plugin to add support for building an executable JVM application.
    application
}

dependencies {
    // Core module for interfaces and data classes
    implementation(project(":core"))
    
    // Utils module for utility functions
    implementation(project(":utils"))
    
    // Apply the kotlinx bundle of dependencies from the version catalog
    implementation(libs.bundles.kotlinxEcosystem)
    
    // Apply the tagify core dependencies
    implementation(libs.bundles.tagifyCore)
    
    // Include plugins at runtime
    runtimeOnly(project(":plugins:local-db"))
    runtimeOnly(project(":plugins:file-system"))
    
    // Test dependencies
    testImplementation(kotlin("test"))
}

application {
    // Define the Fully Qualified Name for the application main class
    mainClass = "com.github.mystery2099.tagify.app.TagifyAppKt"
    
    // Set application name
    applicationName = "tagify"
}
