plugins {
    // Apply the shared build logic from a convention plugin.
    // The shared code is located in `buildSrc/src/main/kotlin/kotlin-jvm.gradle.kts`.
    id("buildsrc.convention.kotlin-jvm")
    
    // Apply Kotlin Serialization plugin from `gradle/libs.versions.toml`.
    alias(libs.plugins.kotlinPluginSerialization)
}

dependencies {
    // Core module for interfaces and data classes
    implementation(project(":core"))
    
    // Utils module for utility functions
    implementation(project(":utils"))
    
    // Apply the kotlinx bundle of dependencies from the version catalog
    implementation(libs.bundles.kotlinxEcosystem)
    
    // Apply the tagify core dependencies
    implementation(libs.bundles.tagifyCore)
    
    // Test dependencies
    testImplementation(kotlin("test"))
}