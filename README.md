# Tagify

A modular, extensible, cross-platform file tagging and search tool built with Kotlin.

## Overview

Tagify is a powerful command-line tool that allows you to organize and find your files using tags instead of traditional folder hierarchies. It's designed to be modular and extensible, with a plugin system that allows you to add new features and functionality.

Key features:
- Tag any file on your system with multiple tags
- Search for files by tag combinations
- Organize tags hierarchically
- Extensible plugin system
- Cross-platform (works on Windows, macOS, and Linux)
- Configurable and localizable

## Architecture

Tagify is built with a modular architecture that separates core functionality from implementations:

- **Core**: Contains interfaces and data classes that define the system's functionality
- **Plugins**: Provide implementations of core interfaces (database, file tracking, etc.)
- **CLI**: Command-line interface for interacting with the system
- **Config/Lang**: Configuration and language files for customization

### Core Components

- **TagManager**: Manages tags (create, update, delete, search)
- **FileTracker**: Tracks files and their associated tags
- **DatabaseProvider**: Provides database access for storing tags and file information
- **PluginHandler**: Manages plugin loading and lifecycle
- **ConfigProvider**: Loads and manages configuration
- **LanguageProvider**: Loads and manages language strings
- **CommandHandler**: Processes CLI commands
- **CacheManager**: Manages in-memory caching

### Technologies Used

- [Kotlin](https://kotlinlang.org/) - Modern, concise programming language
- [Clikt](https://ajalt.github.io/clikt/) - CLI parsing library
- [PF4J](https://pf4j.org/) - Plugin framework
- [HopLite](https://github.com/sksamuel/hoplite) - Configuration library
- [Okio](https://square.github.io/okio/) - I/O library
- [Caffeine](https://github.com/ben-manes/caffeine) - Caching library

## Installation

### Prerequisites

- Java 11 or higher
- Gradle (optional, wrapper included)

### Building from Source

1. Clone the repository:
   ```
   git clone https://github.com/mystery2099/tagify.git
   cd tagify
   ```

2. Build the application:
   ```
   ./gradlew build
   ```

3. Run the application:
   ```
   ./gradlew run
   ```

### Installing Plugins

Plugins are stored in the plugins directory (`~/.tagify/plugins` by default). To install a plugin:

1. Download the plugin JAR file
2. Place it in the plugins directory
3. Restart Tagify or run `tagify plugins load <plugin-id>`

## Usage

### Basic Commands

```
# Add tags to a file
tagify add document.pdf important work

# Add tags recursively to all files in a directory
tagify add --recursive ./photos vacation summer

# Remove tags from a file
tagify remove document.pdf important

# List files with their tags
tagify list

# List files with a specific tag
tagify list --tag important

# Open files with a specific tag
tagify open --tag important

# Manage tags
tagify tags list
tagify tags create work --color "#FF5733"
tagify tags delete old-tag

# Manage plugins
tagify plugins list
tagify plugins install ./my-plugin.jar
tagify plugins uninstall my-plugin

# Manage configuration
tagify config get general.language
tagify config set general.language fr
```

### Configuration

Tagify uses TOML configuration files located in `~/.tagify/config` by default. The main configuration file is `config.toml`.

Key configuration sections:
- `general`: Application settings
- `paths`: Directory paths
- `database`: Database settings
- `plugins`: Plugin settings
- `cache`: Cache settings
- `ui`: User interface settings
- `tags`: Tag settings
- `files`: File settings

### Localization

Tagify supports multiple languages through TOML language files located in `~/.tagify/lang` by default. Language files are named with their ISO language code (e.g., `en.toml`, `fr.toml`).

To change the language, use:
```
tagify config set general.language fr
```

## Extending Tagify

### Creating Plugins

Plugins are JAR files that implement one or more of Tagify's core interfaces. A plugin must include:

1. A plugin class that extends `org.pf4j.Plugin`
2. One or more extension classes that implement Tagify interfaces
3. A plugin descriptor in the JAR manifest

Example plugin structure:
```kotlin
class MyPlugin : Plugin() {
    override fun start() {
        // Plugin initialization
    }
    
    override fun stop() {
        // Plugin cleanup
    }
}

@Extension
class MyDatabaseProvider : DatabaseProvider {
    // Implementation of DatabaseProvider interface
}
```

### Custom Commands

Plugins can add custom commands by implementing the `Command` interface:

```kotlin
@Extension
class MyCustomCommand : Command {
    override fun getName(): String = "custom"
    
    override fun getDescription(): String = "My custom command"
    
    override suspend fun execute(args: List<String>, context: CommandContext): CommandResult {
        // Command implementation
        return CommandResult.success("Custom command executed")
    }
}
```

## Development

This project uses [Gradle](https://gradle.org/) for building and dependency management.

* Run `./gradlew run` to build and run the application.
* Run `./gradlew build` to only build the application.
* Run `./gradlew check` to run all checks, including tests.
* Run `./gradlew clean` to clean all build outputs.

The project follows a multi-module setup:
- `core`: Core interfaces and data classes
- `app`: Main application and CLI
- `plugins`: Plugin implementations
- `utils`: Utility functions

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Kotlin](https://kotlinlang.org/) - For the amazing language
- [Clikt](https://ajalt.github.io/clikt/) - For the CLI parsing
- [PF4J](https://pf4j.org/) - For the plugin framework
- [HopLite](https://github.com/sksamuel/hoplite) - For the configuration library
- [Okio](https://square.github.io/okio/) - For the I/O library
- [Caffeine](https://github.com/ben-manes/caffeine) - For the caching library