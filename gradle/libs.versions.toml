# Version catalog is a central place for you to declare and version dependencies
# https://docs.gradle.org/current/userguide/platforms.html#sub:version-catalog
# https://docs.gradle.org/current/userguide/platforms.html#sub::toml-dependencies-format

[versions]
kotlin = "2.2.0"
kotlinxDatetime = "0.6.1"
kotlinxSerializationJSON = "1.7.3"
kotlinxCoroutines = "1.9.0"
clikt = "4.2.2"
pf4j = "3.10.0"
hoplite = "2.7.5"
okio = "3.7.0"
caffeine = "3.1.8"
slf4j = "2.0.12"
logback = "1.5.3"

[libraries]
kotlinGradlePlugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }
kotlinxDatetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "kotlinxDatetime" }
kotlinxSerialization = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationJSON" }
kotlinxCoroutines = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinxCoroutines" }

# Tagify dependencies
clikt = { module = "com.github.ajalt.clikt:clikt", version.ref = "clikt" }
pf4j = { module = "org.pf4j:pf4j", version.ref = "pf4j" }
hoplite-core = { module = "com.sksamuel.hoplite:hoplite-core", version.ref = "hoplite" }
hoplite-toml = { module = "com.sksamuel.hoplite:hoplite-toml", version.ref = "hoplite" }
okio = { module = "com.squareup.okio:okio", version.ref = "okio" }
caffeine = { module = "com.github.ben-manes.caffeine:caffeine", version.ref = "caffeine" }
slf4j-api = { module = "org.slf4j:slf4j-api", version.ref = "slf4j" }
logback-classic = { module = "ch.qos.logback:logback-classic", version.ref = "logback" }

# Libraries can be bundled together for easier import
[bundles]
kotlinxEcosystem = ["kotlinxDatetime", "kotlinxSerialization", "kotlinxCoroutines"]
tagifyCore = ["clikt", "pf4j", "hoplite-core", "hoplite-toml", "okio", "caffeine", "slf4j-api", "logback-classic"]

[plugins]
kotlinPluginSerialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }