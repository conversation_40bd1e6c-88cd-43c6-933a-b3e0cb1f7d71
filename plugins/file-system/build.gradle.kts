plugins {
    // Apply the shared build logic from a convention plugin.
    id("buildsrc.convention.kotlin-jvm")
    // Apply Kotlin Serialization plugin from `gradle/libs.versions.toml`.
    alias(libs.plugins.kotlinPluginSerialization)
}

dependencies {
    // Depend on the core module for interfaces and data classes
    implementation(project(":core"))
    
    // Apply the kotlinx bundle of dependencies from the version catalog
    implementation(libs.bundles.kotlinxEcosystem)
    
    // PF4J for plugin support
    implementation(libs.pf4j)
    
    // Okio for file I/O
    implementation(libs.okio)
    
    // Test dependencies
    testImplementation(kotlin("test"))
}

// Configure the plugin descriptor
tasks.jar {
    manifest {
        attributes(
            "Plugin-Class" to "com.github.mystery2099.tagify.plugins.filesystem.FileSystemPlugin",
            "Plugin-Id" to "file-system",
            "Plugin-Version" to "1.0.0",
            "Plugin-Description" to "File system tracking plugin for Tagify",
            "Plugin-Provider" to "Mystery2099",
            "Plugin-Dependencies" to ""
        )
    }
}