package com.github.mystery2099.tagify.core.cli

import com.github.mystery2099.tagify.core.api.TagManager
import com.github.mystery2099.tagify.core.api.FileTracker
import com.github.mystery2099.tagify.core.config.ConfigProvider
import com.github.mystery2099.tagify.core.i18n.LanguageProvider
import com.github.mystery2099.tagify.core.plugin.PluginHandler
import java.io.PrintWriter
import java.util.Locale

/**
 * Context for command execution in the Tagify system.
 *
 * The CommandContext provides access to various services and utilities needed for command execution,
 * such as input/output streams, configuration, language strings, and core services.
 *
 * @property input The input reader for the command
 * @property output The output writer for the command
 * @property error The error writer for the command
 * @property configProvider The configuration provider
 * @property languageProvider The language provider
 * @property tagManager The tag manager
 * @property fileTracker The file tracker
 * @property pluginHandler The plugin handler
 * @property locale The locale for the command
 * @property workingDirectory The working directory for the command
 * @property environment The environment variables for the command
 */
data class CommandContext(
    val input: java.io.BufferedReader,
    val output: PrintWriter,
    val error: PrintWriter,
    val configProvider: ConfigProvider,
    val languageProvider: LanguageProvider,
    val tagManager: TagManager,
    val fileTracker: FileTracker,
    val pluginHandler: PluginHandler,
    val locale: Locale = Locale.getDefault(),
    val workingDirectory: java.nio.file.Path = java.nio.file.Path.of("").toAbsolutePath(),
    val environment: Map<String, String> = System.getenv()
) {
    /**
     * Gets a localized string.
     *
     * @param key The key of the language string
     * @param args Arguments to format the string with
     * @return The language string, or the key if the string is not found
     */
    fun getString(key: String, vararg args: Any): String =
        languageProvider.getString(key, locale, *args)

    /**
     * Prints a message to the output.
     *
     * @param message The message to print
     */
    fun print(message: String) {
        output.print(message)
        output.flush()
    }

    /**
     * Prints a message to the output, followed by a newline.
     *
     * @param message The message to print
     */
    fun println(message: String) {
        output.println(message)
        output.flush()
    }

    /**
     * Prints an error message to the error output.
     *
     * @param message The error message to print
     */
    fun printError(message: String) {
        error.println(message)
        error.flush()
    }

    /**
     * Reads a line from the input.
     *
     * @return The line read from the input, or null if the end of the stream has been reached
     */
    fun readLine(): String? = input.readLine()

    /**
     * Prompts the user for input.
     *
     * @param prompt The prompt to display
     * @return The user input, or null if the end of the stream has been reached
     */
    fun prompt(prompt: String): String? {
        print(prompt)
        return readLine()
    }

    /**
     * Gets a configuration value.
     *
     * @param key The key of the configuration value
     * @param type The class of the configuration value
     * @param default The default value to return if the key is not found
     * @return The configuration value, or the default value if the key is not found
     */
    fun <T : Any> getConfig(key: String, type: kotlin.reflect.KClass<T>, default: T? = null): T? =
        configProvider.getConfig(key, type, default)

    /**
     * Resolves a path relative to the working directory.
     *
     * @param path The path to resolve
     * @return The resolved path
     */
    fun resolvePath(path: String): java.nio.file.Path =
        workingDirectory.resolve(path).normalize()
}