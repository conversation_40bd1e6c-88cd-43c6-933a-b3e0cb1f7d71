package com.github.mystery2099.tagify.core.cache

import java.util.concurrent.Callable

/**
 * Interface for a cache in the Tagify system.
 *
 * A cache is a key-value store that provides fast access to frequently used data.
 * It supports operations for getting, putting, and removing entries, as well as
 * querying cache statistics.
 *
 * @param K The type of the cache keys
 * @param V The type of the cache values
 */
interface Cache<K : Any, V : Any> {
    /**
     * Gets the name of the cache.
     *
     * @return The name of the cache
     */
    fun getName(): String

    /**
     * Gets a value from the cache.
     *
     * @param key The key to get the value for
     * @return The value, or null if no value is associated with the key
     */
    fun get(key: K): V?

    /**
     * Gets a value from the cache, computing it if it doesn't exist.
     *
     * @param key The key to get the value for
     * @param loader The function to compute the value if it doesn't exist
     * @return The value
     * @throws RuntimeException if computing the value fails
     */
    fun get(key: K, loader: (K) -> V): V

    /**
     * Gets a value from the cache, computing it if it doesn't exist.
     *
     * @param key The key to get the value for
     * @param loader The callable to compute the value if it doesn't exist
     * @return The value
     * @throws RuntimeException if computing the value fails
     */
    fun get(key: K, loader: Callable<V>): V

    /**
     * Gets all values from the cache for the given keys.
     *
     * @param keys The keys to get the values for
     * @return A map of keys to values, with keys not in the cache omitted
     */
    fun getAll(keys: Iterable<K>): Map<K, V>

    /**
     * Gets all values from the cache for the given keys, computing missing values.
     *
     * @param keys The keys to get the values for
     * @param loader The function to compute values for keys not in the cache
     * @return A map of keys to values
     * @throws RuntimeException if computing a value fails
     */
    fun getAll(keys: Iterable<K>, loader: (K) -> V): Map<K, V>

    /**
     * Puts a value in the cache.
     *
     * @param key The key to put the value under
     * @param value The value to put
     */
    fun put(key: K, value: V)

    /**
     * Puts all values in the cache.
     *
     * @param map The map of keys to values to put
     */
    fun putAll(map: Map<K, V>)

    /**
     * Invalidates a key in the cache.
     *
     * @param key The key to invalidate
     */
    fun invalidate(key: K)

    /**
     * Invalidates all keys in the cache.
     *
     * @param keys The keys to invalidate
     */
    fun invalidateAll(keys: Iterable<K>)

    /**
     * Invalidates all keys in the cache.
     */
    fun invalidateAll()

    /**
     * Gets the approximate number of entries in the cache.
     *
     * @return The approximate number of entries
     */
    fun size(): Long

    /**
     * Gets the statistics for the cache.
     *
     * @return The cache statistics
     */
    fun stats(): CacheStats

    /**
     * Checks if a key is present in the cache.
     *
     * @param key The key to check
     * @return true if the key is present, false otherwise
     */
    fun containsKey(key: K): Boolean

    /**
     * Cleans up the cache, removing expired entries.
     */
    fun cleanup()
}