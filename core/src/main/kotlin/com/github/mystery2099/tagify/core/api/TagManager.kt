package com.github.mystery2099.tagify.core.api

import com.github.mystery2099.tagify.core.model.Tag
import org.pf4j.ExtensionPoint

/**
 * Interface for managing tags in the Tagify system.
 *
 * The TagManager is responsible for creating, retrieving, updating, and deleting tags,
 * as well as managing tag hierarchies and relationships.
 */
interface TagManager : ExtensionPoint {
    /**
     * Creates a new tag.
     *
     * @param tag The tag to create
     * @return The created tag, potentially with additional metadata
     * @throws IllegalArgumentException if a tag with the same ID already exists
     */
    suspend fun createTag(tag: Tag): Tag

    /**
     * Retrieves a tag by its ID.
     *
     * @param id The ID of the tag to retrieve
     * @return The tag, or null if no tag with the given ID exists
     */
    suspend fun getTag(id: String): Tag?

    /**
     * Updates an existing tag.
     *
     * @param tag The tag with updated information
     * @return The updated tag, or null if no tag with the given ID exists
     */
    suspend fun updateTag(tag: Tag): Tag?

    /**
     * Deletes a tag by its ID.
     *
     * @param id The ID of the tag to delete
     * @return true if the tag was deleted, false if no tag with the given ID exists
     */
    suspend fun deleteTag(id: String): Boolean

    /**
     * Lists all tags.
     *
     * @return A list of all tags
     */
    suspend fun listTags(): List<Tag>

    /**
     * Searches for tags matching the given query.
     *
     * @param query The search query
     * @return A list of tags matching the query
     */
    suspend fun searchTags(query: String): List<Tag>

    /**
     * Gets all child tags of a parent tag.
     *
     * @param parentId The ID of the parent tag
     * @return A list of child tags
     */
    suspend fun getChildTags(parentId: String): List<Tag>

    /**
     * Gets the parent tag of a child tag.
     *
     * @param childId The ID of the child tag
     * @return The parent tag, or null if the tag has no parent
     */
    suspend fun getParentTag(childId: String): Tag?

    /**
     * Gets all tags associated with a file.
     *
     * @param fileId The ID of the file
     * @return A list of tags associated with the file
     */
    suspend fun getTagsForFile(fileId: String): List<Tag>

    /**
     * Gets all files associated with a tag.
     *
     * @param tagId The ID of the tag
     * @return A list of file IDs associated with the tag
     */
    suspend fun getFilesForTag(tagId: String): List<String>
}