package com.github.mystery2099.tagify.core.cli

/**
 * Interface for handling commands in the Tagify system.
 *
 * The CommandHandler is responsible for registering, finding, and executing commands.
 * It manages the command hierarchy and provides utilities for command execution.
 */
interface CommandHandler {
    /**
     * Registers a command.
     *
     * @param command The command to register
     * @throws IllegalArgumentException if a command with the same name is already registered
     */
    fun registerCommand(command: Command)

    /**
     * Unregisters a command.
     *
     * @param commandName The name of the command to unregister
     * @return true if the command was unregistered, false if no command with the given name exists
     */
    fun unregisterCommand(commandName: String): Boolean

    /**
     * Finds a command by name.
     *
     * @param commandName The name of the command to find
     * @return The command, or null if no command with the given name exists
     */
    fun findCommand(commandName: String): Command?

    /**
     * Gets all registered commands.
     *
     * @return A list of all registered commands
     */
    fun getAllCommands(): List<Command>

    /**
     * Gets all top-level commands.
     *
     * @return A list of all top-level commands
     */
    fun getTopLevelCommands(): List<Command>

    /**
     * Executes a command with the given arguments.
     *
     * @param commandName The name of the command to execute
     * @param args The command arguments
     * @param context The command execution context
     * @return The result of the command execution
     * @throws IllegalArgumentException if the command is not found
     * @throws RuntimeException if execution fails
     */
    suspend fun executeCommand(commandName: String, args: List<String>, context: CommandContext): CommandResult

    /**
     * Parses a command line and executes the corresponding command.
     *
     * @param commandLine The command line to parse
     * @param context The command execution context
     * @return The result of the command execution
     * @throws IllegalArgumentException if the command is not found
     * @throws RuntimeException if execution fails
     */
    suspend fun parseAndExecute(commandLine: String, context: CommandContext): CommandResult

    /**
     * Gets the help text for a command.
     *
     * @param commandName The name of the command
     * @return The help text for the command, or null if no command with the given name exists
     */
    fun getCommandHelp(commandName: String): String?

    /**
     * Gets the usage examples for a command.
     *
     * @param commandName The name of the command
     * @return The usage examples for the command, or an empty list if no command with the given name exists
     */
    fun getCommandExamples(commandName: String): List<String>

    /**
     * Gets the subcommands of a command.
     *
     * @param commandName The name of the command
     * @return The subcommands of the command, or an empty list if no command with the given name exists
     */
    fun getSubcommands(commandName: String): List<Command>

    /**
     * Checks if a command is deprecated.
     *
     * @param commandName The name of the command
     * @return true if the command is deprecated, false otherwise or if no command with the given name exists
     */
    fun isCommandDeprecated(commandName: String): Boolean

    /**
     * Gets the deprecation message for a command.
     *
     * @param commandName The name of the command
     * @return The deprecation message for the command, or null if the command is not deprecated or does not exist
     */
    fun getCommandDeprecationMessage(commandName: String): String?
}