package com.github.mystery2099.tagify.core.api

import org.pf4j.ExtensionPoint
import kotlin.reflect.KClass

/**
 * Interface for providing database access in the Tagify system.
 *
 * The DatabaseProvider is responsible for managing database connections, transactions,
 * and data access operations. It provides a generic interface for storing and retrieving
 * data, regardless of the underlying database technology.
 */
interface DatabaseProvider : ExtensionPoint {
    /**
     * Gets the name of the database provider.
     *
     * @return The name of the database provider
     */
    fun getName(): String

    /**
     * Gets the version of the database provider.
     *
     * @return The version of the database provider
     */
    fun getVersion(): String

    /**
     * Initializes the database provider with the given configuration.
     *
     * @param config Configuration parameters for the database
     * @throws IllegalArgumentException if the configuration is invalid
     * @throws RuntimeException if initialization fails
     */
    suspend fun initialize(config: Map<String, Any>)

    /**
     * Checks if the database provider is initialized and ready to use.
     *
     * @return true if the database provider is initialized, false otherwise
     */
    fun isInitialized(): <PERSON><PERSON><PERSON>

    /**
     * Closes the database provider and releases any resources.
     *
     * @throws RuntimeException if closing fails
     */
    suspend fun close()

    /**
     * Stores an object in the database.
     *
     * @param key The key to store the object under
     * @param value The object to store
     * @param collection The collection to store the object in
     * @throws IllegalArgumentException if the key or value is invalid
     * @throws RuntimeException if storing fails
     */
    suspend fun <T : Any> store(key: String, value: T, collection: String)

    /**
     * Retrieves an object from the database.
     *
     * @param key The key to retrieve the object for
     * @param type The class of the object to retrieve
     * @param collection The collection to retrieve the object from
     * @return The retrieved object, or null if no object with the given key exists
     * @throws RuntimeException if retrieval fails
     */
    suspend fun <T : Any> retrieve(key: String, type: KClass<T>, collection: String): T?

    /**
     * Deletes an object from the database.
     *
     * @param key The key of the object to delete
     * @param collection The collection to delete the object from
     * @return true if the object was deleted, false if no object with the given key exists
     * @throws RuntimeException if deletion fails
     */
    suspend fun delete(key: String, collection: String): Boolean

    /**
     * Lists all keys in a collection.
     *
     * @param collection The collection to list keys from
     * @return A list of all keys in the collection
     * @throws RuntimeException if listing fails
     */
    suspend fun listKeys(collection: String): List<String>

    /**
     * Searches for objects in a collection matching the given query.
     *
     * @param query The search query
     * @param type The class of the objects to search for
     * @param collection The collection to search in
     * @return A list of objects matching the query
     * @throws IllegalArgumentException if the query is invalid
     * @throws RuntimeException if searching fails
     */
    suspend fun <T : Any> search(query: String, type: KClass<T>, collection: String): List<T>

    /**
     * Executes a transaction with the given operations.
     *
     * @param operations The operations to execute in the transaction
     * @return true if the transaction was successful, false otherwise
     * @throws RuntimeException if the transaction fails
     */
    suspend fun executeTransaction(operations: suspend DatabaseTransaction.() -> Unit): Boolean

    /**
     * Interface for database transactions.
     */
    interface DatabaseTransaction {
        /**
         * Stores an object in the database as part of the transaction.
         *
         * @param key The key to store the object under
         * @param value The object to store
         * @param collection The collection to store the object in
         * @throws IllegalArgumentException if the key or value is invalid
         */
        suspend fun <T : Any> store(key: String, value: T, collection: String)

        /**
         * Retrieves an object from the database as part of the transaction.
         *
         * @param key The key to retrieve the object for
         * @param type The class of the object to retrieve
         * @param collection The collection to retrieve the object from
         * @return The retrieved object, or null if no object with the given key exists
         */
        suspend fun <T : Any> retrieve(key: String, type: KClass<T>, collection: String): T?

        /**
         * Deletes an object from the database as part of the transaction.
         *
         * @param key The key of the object to delete
         * @param collection The collection to delete the object from
         * @return true if the object was deleted, false if no object with the given key exists
         */
        suspend fun delete(key: String, collection: String): Boolean

        /**
         * Rolls back the transaction.
         */
        fun rollback()
    }
}