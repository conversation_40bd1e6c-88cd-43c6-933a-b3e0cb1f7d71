package com.github.mystery2099.tagify.core.cache

/**
 * Interface for cache statistics in the Tagify system.
 *
 * CacheStats provides information about the performance and usage of a cache,
 * including hit/miss counts, load times, and eviction counts.
 */
interface CacheStats {
    /**
     * Gets the number of times a cache lookup resulted in a hit.
     *
     * @return The number of hits
     */
    fun hitCount(): Long

    /**
     * Gets the number of times a cache lookup resulted in a miss.
     *
     * @return The number of misses
     */
    fun missCount(): Long

    /**
     * Gets the number of times a new value was loaded into the cache.
     *
     * @return The number of loads
     */
    fun loadCount(): Long

    /**
     * Gets the number of times a value was evicted from the cache.
     *
     * @return The number of evictions
     */
    fun evictionCount(): Long

    /**
     * Gets the total time spent loading new values into the cache.
     *
     * @return The total load time in nanoseconds
     */
    fun totalLoadTime(): Long

    /**
     * Gets the average time spent loading new values into the cache.
     *
     * @return The average load time in nanoseconds, or 0 if no values have been loaded
     */
    fun averageLoadTime(): Double

    /**
     * Gets the hit rate of the cache.
     *
     * @return The hit rate as a value between 0.0 and 1.0, or 0.0 if no requests have been made
     */
    fun hitRate(): Double

    /**
     * Gets the miss rate of the cache.
     *
     * @return The miss rate as a value between 0.0 and 1.0, or 0.0 if no requests have been made
     */
    fun missRate(): Double

    /**
     * Gets the total number of requests to the cache.
     *
     * @return The total number of requests (hits + misses)
     */
    fun requestCount(): Long

    /**
     * Gets the number of successful loads.
     *
     * @return The number of successful loads
     */
    fun loadSuccessCount(): Long

    /**
     * Gets the number of failed loads.
     *
     * @return The number of failed loads
     */
    fun loadFailureCount(): Long

    /**
     * Gets the load failure rate.
     *
     * @return The load failure rate as a value between 0.0 and 1.0, or 0.0 if no loads have been attempted
     */
    fun loadFailureRate(): Double

    /**
     * Gets a snapshot of the cache statistics at the current point in time.
     *
     * @return A new CacheStats instance representing the current statistics
     */
    fun snapshot(): CacheStats

    /**
     * Gets a string representation of the cache statistics.
     *
     * @return A string representation of the cache statistics
     */
    override fun toString(): String
}