package com.github.mystery2099.tagify.core.plugin

import org.pf4j.PluginDescriptor
import org.pf4j.PluginState
import java.nio.file.Path

/**
 * Interface for managing plugins in the Tagify system.
 *
 * The PluginHandler is responsible for loading, unloading, enabling, disabling,
 * and providing information about plugins.
 */
interface PluginHandler {
    /**
     * Initializes the plugin handler with the given plugin directory.
     *
     * @param pluginDirectory The directory containing plugins
     * @throws IllegalArgumentException if the directory is invalid
     * @throws RuntimeException if initialization fails
     */
    suspend fun initialize(pluginDirectory: Path)

    /**
     * Gets the plugin directory.
     *
     * @return The directory containing plugins
     */
    fun getPluginDirectory(): Path

    /**
     * Loads all plugins from the plugin directory.
     *
     * @return A list of plugin IDs that were loaded
     * @throws RuntimeException if loading fails
     */
    suspend fun loadPlugins(): List<String>

    /**
     * Loads a specific plugin.
     *
     * @param pluginId The ID of the plugin to load
     * @return true if the plugin was loaded, false otherwise
     * @throws RuntimeException if loading fails
     */
    suspend fun loadPlugin(pluginId: String): Boolean

    /**
     * Unloads a specific plugin.
     *
     * @param pluginId The ID of the plugin to unload
     * @return true if the plugin was unloaded, false otherwise
     * @throws RuntimeException if unloading fails
     */
    suspend fun unloadPlugin(pluginId: String): Boolean

    /**
     * Enables a specific plugin.
     *
     * @param pluginId The ID of the plugin to enable
     * @return true if the plugin was enabled, false otherwise
     * @throws RuntimeException if enabling fails
     */
    suspend fun enablePlugin(pluginId: String): Boolean

    /**
     * Disables a specific plugin.
     *
     * @param pluginId The ID of the plugin to disable
     * @return true if the plugin was disabled, false otherwise
     * @throws RuntimeException if disabling fails
     */
    suspend fun disablePlugin(pluginId: String): Boolean

    /**
     * Gets information about a specific plugin.
     *
     * @param pluginId The ID of the plugin
     * @return The plugin information, or null if no plugin with the given ID exists
     */
    fun getPluginInfo(pluginId: String): PluginInfo?

    /**
     * Gets information about all plugins.
     *
     * @return A list of plugin information for all plugins
     */
    fun getAllPluginInfo(): List<PluginInfo>

    /**
     * Gets the state of a specific plugin.
     *
     * @param pluginId The ID of the plugin
     * @return The plugin state, or null if no plugin with the given ID exists
     */
    fun getPluginState(pluginId: String): PluginState?

    /**
     * Gets the descriptor of a specific plugin.
     *
     * @param pluginId The ID of the plugin
     * @return The plugin descriptor, or null if no plugin with the given ID exists
     */
    fun getPluginDescriptor(pluginId: String): PluginDescriptor?

    /**
     * Checks if a plugin is loaded.
     *
     * @param pluginId The ID of the plugin
     * @return true if the plugin is loaded, false otherwise
     */
    fun isPluginLoaded(pluginId: String): Boolean

    /**
     * Checks if a plugin is enabled.
     *
     * @param pluginId The ID of the plugin
     * @return true if the plugin is enabled, false otherwise
     */
    fun isPluginEnabled(pluginId: String): Boolean

    /**
     * Gets all extensions of a specific type.
     *
     * @param extensionClass The class of the extension
     * @return A list of all extensions of the specified type
     */
    fun <T> getExtensions(extensionClass: Class<T>): List<T>

    /**
     * Gets all extensions of a specific type from a specific plugin.
     *
     * @param extensionClass The class of the extension
     * @param pluginId The ID of the plugin
     * @return A list of extensions of the specified type from the specified plugin
     */
    fun <T> getExtensions(extensionClass: Class<T>, pluginId: String): List<T>

    /**
     * Refreshes the plugin registry, reloading plugin information.
     *
     * @return true if the refresh was successful, false otherwise
     * @throws RuntimeException if refreshing fails
     */
    suspend fun refreshRegistry(): Boolean

    /**
     * Stops all plugins and releases resources.
     *
     * @throws RuntimeException if stopping fails
     */
    suspend fun shutdown()
}