package com.github.mystery2099.tagify.core.cache

import java.time.Duration
import kotlin.reflect.KClass

/**
 * Interface for managing caches in the Tagify system.
 *
 * The CacheManager is responsible for creating, accessing, and managing caches
 * for various parts of the system. It provides a unified interface for caching
 * regardless of the underlying cache implementation.
 */
interface CacheManager {
    /**
     * Gets a cache with the given name and configuration.
     *
     * @param name The name of the cache
     * @param keyType The class of the cache keys
     * @param valueType The class of the cache values
     * @param maximumSize The maximum number of entries in the cache
     * @param expireAfterWrite The duration after which entries should expire after being written
     * @param expireAfterAccess The duration after which entries should expire after being accessed
     * @return The cache
     */
    fun <K : Any, V : Any> getCache(
        name: String,
        keyType: KClass<K>,
        valueType: KClass<V>,
        maximumSize: Long = 10000,
        expireAfterWrite: Duration? = null,
        expireAfterAccess: Duration? = null
    ): Cache<K, V>

    /**
     * Gets a cache with the given name, creating it if it doesn't exist.
     *
     * @param name The name of the cache
     * @param keyType The class of the cache keys
     * @param valueType The class of the cache values
     * @return The cache, or null if no cache with the given name exists
     */
    fun <K : Any, V : Any> getCacheIfExists(
        name: String,
        keyType: KClass<K>,
        valueType: KClass<V>
    ): Cache<K, V>?

    /**
     * Checks if a cache with the given name exists.
     *
     * @param name The name of the cache
     * @return true if a cache with the given name exists, false otherwise
     */
    fun hasCache(name: String): Boolean

    /**
     * Removes a cache with the given name.
     *
     * @param name The name of the cache
     * @return true if the cache was removed, false if no cache with the given name exists
     */
    fun removeCache(name: String): Boolean

    /**
     * Gets all cache names.
     *
     * @return A list of all cache names
     */
    fun getCacheNames(): List<String>

    /**
     * Gets statistics for a cache.
     *
     * @param name The name of the cache
     * @return The cache statistics, or null if no cache with the given name exists
     */
    fun getCacheStats(name: String): CacheStats?

    /**
     * Gets statistics for all caches.
     *
     * @return A map of cache names to cache statistics
     */
    fun getAllCacheStats(): Map<String, CacheStats>

    /**
     * Clears a cache.
     *
     * @param name The name of the cache
     * @return true if the cache was cleared, false if no cache with the given name exists
     */
    fun clearCache(name: String): Boolean

    /**
     * Clears all caches.
     */
    fun clearAllCaches()

    /**
     * Shuts down the cache manager and releases resources.
     */
    fun shutdown()
}