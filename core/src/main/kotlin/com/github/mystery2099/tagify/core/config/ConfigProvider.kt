package com.github.mystery2099.tagify.core.config

import org.pf4j.ExtensionPoint
import java.nio.file.Path
import kotlin.reflect.KClass

/**
 * Interface for providing configuration in the Tagify system.
 *
 * The ConfigProvider is responsible for loading, parsing, and accessing configuration
 * from various sources, such as TOML files, environment variables, or command-line arguments.
 */
interface ConfigProvider : ExtensionPoint {
    /**
     * Gets the name of the configuration format.
     *
     * @return The name of the configuration format (e.g., "TOML", "JSON", "YAML")
     */
    fun getFormatName(): String

    /**
     * Gets the file extensions supported by this provider.
     *
     * @return A list of file extensions (without the dot) supported by this provider
     */
    fun getSupportedExtensions(): List<String>

    /**
     * Initializes the configuration provider with the given base directory.
     *
     * @param configDirectory The directory containing configuration files
     * @throws IllegalArgumentException if the directory is invalid
     * @throws RuntimeException if initialization fails
     */
    suspend fun initialize(configDirectory: Path)

    /**
     * Loads a configuration file.
     *
     * @param path The path to the configuration file
     * @throws IllegalArgumentException if the file is invalid
     * @throws RuntimeException if loading fails
     */
    suspend fun loadConfig(path: Path)

    /**
     * Loads a configuration file with the given name from the config directory.
     *
     * @param name The name of the configuration file (without extension)
     * @throws IllegalArgumentException if the file is invalid
     * @throws RuntimeException if loading fails
     */
    suspend fun loadConfig(name: String)

    /**
     * Gets a configuration value.
     *
     * @param key The key of the configuration value
     * @param type The class of the configuration value
     * @param default The default value to return if the key is not found
     * @return The configuration value, or the default value if the key is not found
     * @throws IllegalArgumentException if the key is invalid
     * @throws RuntimeException if getting the value fails
     */
    fun <T : Any> getConfig(key: String, type: KClass<T>, default: T? = null): T?

    /**
     * Gets a configuration section.
     *
     * @param key The key of the configuration section
     * @param type The class of the configuration section
     * @return The configuration section, or null if the key is not found
     * @throws IllegalArgumentException if the key is invalid
     * @throws RuntimeException if getting the section fails
     */
    fun <T : Any> getConfigSection(key: String, type: KClass<T>): T?

    /**
     * Gets all configuration keys.
     *
     * @return A list of all configuration keys
     * @throws RuntimeException if getting the keys fails
     */
    fun getConfigKeys(): List<String>

    /**
     * Checks if a configuration key exists.
     *
     * @param key The key to check
     * @return true if the key exists, false otherwise
     * @throws IllegalArgumentException if the key is invalid
     * @throws RuntimeException if checking fails
     */
    fun hasConfig(key: String): Boolean

    /**
     * Sets a configuration value.
     *
     * @param key The key of the configuration value
     * @param value The configuration value
     * @throws IllegalArgumentException if the key or value is invalid
     * @throws RuntimeException if setting the value fails
     */
    suspend fun setConfig(key: String, value: Any)

    /**
     * Saves the current configuration to a file.
     *
     * @param path The path to save the configuration to
     * @throws RuntimeException if saving fails
     */
    suspend fun saveConfig(path: Path)

    /**
     * Saves the current configuration to a file with the given name in the config directory.
     *
     * @param name The name of the configuration file (without extension)
     * @throws RuntimeException if saving fails
     */
    suspend fun saveConfig(name: String)

    /**
     * Reloads the configuration from the last loaded file.
     *
     * @throws RuntimeException if reloading fails
     */
    suspend fun reloadConfig()

    /**
     * Gets the path to the last loaded configuration file.
     *
     * @return The path to the last loaded configuration file, or null if no file has been loaded
     */
    fun getConfigPath(): Path?

    /**
     * Gets the directory containing configuration files.
     *
     * @return The directory containing configuration files
     */
    fun getConfigDirectory(): Path
}