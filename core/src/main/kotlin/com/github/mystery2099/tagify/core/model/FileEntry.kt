package com.github.mystery2099.tagify.core.model

import kotlinx.serialization.Serializable
import java.nio.file.Path
import kotlin.io.path.absolutePathString
import kotlin.io.path.exists
import kotlin.io.path.isRegularFile
import kotlin.io.path.name

/**
 * Represents a file in the Tagify system that can be tagged and searched.
 *
 * FileEntry objects store metadata about files, including their path, associated tags,
 * and timestamps for creation and modification.
 *
 * @property id Unique identifier for the file entry
 * @property path Absolute path to the file
 * @property tags Set of tag IDs associated with this file
 * @property name Optional custom name for the file (defaults to the filename)
 * @property description Optional description of the file
 * @property created Timestamp when the file entry was created
 * @property modified Timestamp when the file entry was last modified
 * @property lastAccessed Timestamp when the file was last accessed through Tagify
 */
@Serializable
data class FileEntry(
    val id: String,
    val path: String,
    val tags: Set<String> = emptySet(),
    val name: String? = null,
    val description: String? = null,
    val created: Long = System.currentTimeMillis(),
    val modified: Long = created,
    val lastAccessed: Long = created
) {
    /**
     * Gets the display name for the file (custom name or filename).
     *
     * @return The custom name if set, otherwise the filename from the path
     */
    fun displayName(): String = name ?: Path.of(path).name

    /**
     * Checks if the file exists on disk.
     *
     * @return true if the file exists and is a regular file, false otherwise
     */
    fun exists(): Boolean {
        val filePath = Path.of(path)
        return filePath.exists() && filePath.isRegularFile()
    }

    /**
     * Adds a tag to this file entry.
     *
     * @param tagId The ID of the tag to add
     * @return A new FileEntry with the tag added and updated modification time
     */
    fun addTag(tagId: String): FileEntry = 
        if (tags.contains(tagId)) this
        else copy(tags = tags + tagId, modified = System.currentTimeMillis())

    /**
     * Removes a tag from this file entry.
     *
     * @param tagId The ID of the tag to remove
     * @return A new FileEntry with the tag removed and updated modification time
     */
    fun removeTag(tagId: String): FileEntry = 
        if (!tags.contains(tagId)) this
        else copy(tags = tags - tagId, modified = System.currentTimeMillis())

    /**
     * Updates the last accessed timestamp to the current time.
     *
     * @return A new FileEntry with the updated last accessed time
     */
    fun markAccessed(): FileEntry = copy(lastAccessed = System.currentTimeMillis())

    companion object {
        /**
         * Creates a new file entry with a random UUID as the ID.
         *
         * @param path Path to the file (will be converted to an absolute path)
         * @param tags Initial set of tag IDs to associate with the file
         * @param name Optional custom name for the file
         * @param description Optional description of the file
         * @return A new FileEntry instance
         */
        fun create(
            path: Path,
            tags: Set<String> = emptySet(),
            name: String? = null,
            description: String? = null
        ): FileEntry = FileEntry(
            id = java.util.UUID.randomUUID().toString(),
            path = path.absolutePathString(),
            tags = tags,
            name = name,
            description = description
        )

        /**
         * Creates a new file entry with a random UUID as the ID.
         *
         * @param path String representation of the path to the file
         * @param tags Initial set of tag IDs to associate with the file
         * @param name Optional custom name for the file
         * @param description Optional description of the file
         * @return A new FileEntry instance
         */
        fun create(
            path: String,
            tags: Set<String> = emptySet(),
            name: String? = null,
            description: String? = null
        ): FileEntry = create(Path.of(path), tags, name, description)
    }
}