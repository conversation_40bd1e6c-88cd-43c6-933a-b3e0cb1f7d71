package com.github.mystery2099.tagify.core.model

import kotlinx.serialization.Serializable
import java.time.Instant

/**
 * Represents a tag that can be applied to files.
 *
 * Tags are the fundamental unit of organization in Tagify. They can be applied to files
 * and used for searching and filtering.
 *
 * @property id Unique identifier for the tag
 * @property name Human-readable name of the tag
 * @property color Optional color for visual representation (hex format: #RRGGBB)
 * @property description Optional description of the tag's purpose
 * @property parent Optional parent tag for hierarchical organization
 * @property created Timestamp when the tag was created
 * @property modified Timestamp when the tag was last modified
 */
@Serializable
data class Tag(
    val id: String,
    val name: String,
    val color: String? = null,
    val description: String? = null,
    val parent: String? = null,
    val created: Long = System.currentTimeMillis(),
    val modified: Long = created
) {
    /**
     * Checks if this tag is a child of another tag.
     *
     * @param parentId The ID of the potential parent tag
     * @return true if this tag is a child of the specified parent, false otherwise
     */
    fun isChildOf(parentId: String): Boolean = parent == parentId

    /**
     * Creates a copy of this tag with updated modification time.
     *
     * @return A new Tag instance with the current time as the modification time
     */
    fun touch(): Tag = copy(modified = System.currentTimeMillis())

    /**
     * Creates a copy of this tag with a new parent.
     *
     * @param newParent The ID of the new parent tag, or null to remove the parent
     * @return A new Tag instance with the updated parent and modification time
     */
    fun withParent(newParent: String?): Tag = copy(parent = newParent, modified = System.currentTimeMillis())

    companion object {
        /**
         * Creates a new tag with a random UUID as the ID.
         *
         * @param name Human-readable name of the tag
         * @param color Optional color for visual representation (hex format: #RRGGBB)
         * @param description Optional description of the tag's purpose
         * @param parent Optional parent tag for hierarchical organization
         * @return A new Tag instance
         */
        fun create(
            name: String,
            color: String? = null,
            description: String? = null,
            parent: String? = null
        ): Tag = Tag(
            id = java.util.UUID.randomUUID().toString(),
            name = name,
            color = color,
            description = description,
            parent = parent
        )
    }
}