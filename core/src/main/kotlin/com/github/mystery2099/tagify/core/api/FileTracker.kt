package com.github.mystery2099.tagify.core.api

import com.github.mystery2099.tagify.core.model.FileEntry
import org.pf4j.ExtensionPoint
import java.nio.file.Path

/**
 * Interface for tracking files in the Tagify system.
 *
 * The FileTracker is responsible for creating, retrieving, updating, and deleting file entries,
 * as well as managing file metadata and tags.
 */
interface FileTracker : ExtensionPoint {
    /**
     * Creates a new file entry.
     *
     * @param fileEntry The file entry to create
     * @return The created file entry, potentially with additional metadata
     * @throws IllegalArgumentException if a file entry with the same ID already exists
     * @throws IllegalArgumentException if the file does not exist
     */
    suspend fun createFileEntry(fileEntry: FileEntry): FileEntry

    /**
     * Retrieves a file entry by its ID.
     *
     * @param id The ID of the file entry to retrieve
     * @return The file entry, or null if no file entry with the given ID exists
     */
    suspend fun getFileEntry(id: String): FileEntry?

    /**
     * Retrieves a file entry by its path.
     *
     * @param path The path of the file
     * @return The file entry, or null if no file entry with the given path exists
     */
    suspend fun getFileEntryByPath(path: Path): FileEntry?

    /**
     * Updates an existing file entry.
     *
     * @param fileEntry The file entry with updated information
     * @return The updated file entry, or null if no file entry with the given ID exists
     */
    suspend fun updateFileEntry(fileEntry: FileEntry): FileEntry?

    /**
     * Deletes a file entry by its ID.
     *
     * @param id The ID of the file entry to delete
     * @return true if the file entry was deleted, false if no file entry with the given ID exists
     */
    suspend fun deleteFileEntry(id: String): Boolean

    /**
     * Lists all file entries.
     *
     * @return A list of all file entries
     */
    suspend fun listFileEntries(): List<FileEntry>

    /**
     * Searches for file entries matching the given query.
     *
     * @param query The search query
     * @return A list of file entries matching the query
     */
    suspend fun searchFileEntries(query: String): List<FileEntry>

    /**
     * Gets all file entries with a specific tag.
     *
     * @param tagId The ID of the tag
     * @return A list of file entries with the specified tag
     */
    suspend fun getFileEntriesWithTag(tagId: String): List<FileEntry>

    /**
     * Gets all file entries with all of the specified tags.
     *
     * @param tagIds The IDs of the tags
     * @return A list of file entries with all of the specified tags
     */
    suspend fun getFileEntriesWithAllTags(tagIds: Set<String>): List<FileEntry>

    /**
     * Gets all file entries with any of the specified tags.
     *
     * @param tagIds The IDs of the tags
     * @return A list of file entries with any of the specified tags
     */
    suspend fun getFileEntriesWithAnyTag(tagIds: Set<String>): List<FileEntry>

    /**
     * Adds a tag to a file entry.
     *
     * @param fileId The ID of the file entry
     * @param tagId The ID of the tag to add
     * @return The updated file entry, or null if no file entry with the given ID exists
     */
    suspend fun addTagToFile(fileId: String, tagId: String): FileEntry?

    /**
     * Removes a tag from a file entry.
     *
     * @param fileId The ID of the file entry
     * @param tagId The ID of the tag to remove
     * @return The updated file entry, or null if no file entry with the given ID exists
     */
    suspend fun removeTagFromFile(fileId: String, tagId: String): FileEntry?

    /**
     * Scans a directory for files and creates file entries for them.
     *
     * @param directory The directory to scan
     * @param recursive Whether to scan subdirectories recursively
     * @param filter Optional filter to apply to files
     * @return A list of created or updated file entries
     */
    suspend fun scanDirectory(
        directory: Path,
        recursive: Boolean = true,
        filter: (Path) -> Boolean = { true }
    ): List<FileEntry>

    /**
     * Checks if a file exists and updates its status in the system.
     *
     * @param fileId The ID of the file entry
     * @return true if the file exists, false otherwise
     */
    suspend fun checkFileExists(fileId: String): Boolean

    /**
     * Updates the last accessed timestamp for a file entry.
     *
     * @param fileId The ID of the file entry
     * @return The updated file entry, or null if no file entry with the given ID exists
     */
    suspend fun markFileAccessed(fileId: String): FileEntry?
}