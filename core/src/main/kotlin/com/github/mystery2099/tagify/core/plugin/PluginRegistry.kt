package com.github.mystery2099.tagify.core.plugin

import org.pf4j.ExtensionPoint
import kotlin.reflect.KClass

/**
 * Interface for registering and managing plugin extensions in the Tagify system.
 *
 * The PluginRegistry is responsible for registering, finding, and managing extensions
 * provided by plugins. It acts as a central registry for all plugin-provided functionality.
 */
interface PluginRegistry {
    /**
     * Registers an extension.
     *
     * @param extension The extension to register
     * @param extensionClass The class of the extension
     * @param pluginId The ID of the plugin providing the extension
     * @throws IllegalArgumentException if the extension is invalid
     */
    fun <T : ExtensionPoint> registerExtension(extension: T, extensionClass: KClass<T>, pluginId: String)

    /**
     * Unregisters all extensions provided by a plugin.
     *
     * @param pluginId The ID of the plugin
     * @return The number of extensions unregistered
     */
    fun unregisterExtensions(pluginId: String): Int

    /**
     * Gets all extensions of a specific type.
     *
     * @param extensionClass The class of the extension
     * @return A list of all extensions of the specified type
     */
    fun <T : ExtensionPoint> getExtensions(extensionClass: KClass<T>): List<T>

    /**
     * Gets all extensions of a specific type from a specific plugin.
     *
     * @param extensionClass The class of the extension
     * @param pluginId The ID of the plugin
     * @return A list of extensions of the specified type from the specified plugin
     */
    fun <T : ExtensionPoint> getExtensions(extensionClass: KClass<T>, pluginId: String): List<T>

    /**
     * Gets the first extension of a specific type.
     *
     * @param extensionClass The class of the extension
     * @return The first extension of the specified type, or null if no extensions of that type exist
     */
    fun <T : ExtensionPoint> getFirstExtension(extensionClass: KClass<T>): T?

    /**
     * Gets the plugin ID that provides an extension.
     *
     * @param extension The extension
     * @return The plugin ID, or null if the extension is not registered
     */
    fun getPluginId(extension: ExtensionPoint): String?

    /**
     * Checks if a plugin provides an extension of a specific type.
     *
     * @param pluginId The ID of the plugin
     * @param extensionClass The class of the extension
     * @return true if the plugin provides an extension of the specified type, false otherwise
     */
    fun <T : ExtensionPoint> hasExtension(pluginId: String, extensionClass: KClass<T>): Boolean

    /**
     * Gets all extension types registered in the system.
     *
     * @return A list of all extension types
     */
    fun getExtensionTypes(): List<KClass<out ExtensionPoint>>

    /**
     * Gets all plugin IDs that provide extensions.
     *
     * @return A list of all plugin IDs
     */
    fun getPluginIds(): List<String>

    /**
     * Gets all extension types provided by a plugin.
     *
     * @param pluginId The ID of the plugin
     * @return A list of all extension types provided by the plugin
     */
    fun getExtensionTypes(pluginId: String): List<KClass<out ExtensionPoint>>

    /**
     * Gets the number of extensions registered in the system.
     *
     * @return The number of extensions
     */
    fun getExtensionCount(): Int

    /**
     * Gets the number of extensions of a specific type.
     *
     * @param extensionClass The class of the extension
     * @return The number of extensions of the specified type
     */
    fun <T : ExtensionPoint> getExtensionCount(extensionClass: KClass<T>): Int

    /**
     * Gets the number of extensions provided by a plugin.
     *
     * @param pluginId The ID of the plugin
     * @return The number of extensions provided by the plugin
     */
    fun getExtensionCount(pluginId: String): Int

    /**
     * Clears all registered extensions.
     */
    fun clearRegistry()
}