package com.github.mystery2099.tagify.core.cli

import org.pf4j.ExtensionPoint

/**
 * Interface for CLI commands in the Tagify system.
 *
 * Commands are the primary way for users to interact with the Tagify system through the CLI.
 * Each command has a name, description, and execution logic.
 */
interface Command : ExtensionPoint {
    /**
     * Gets the name of the command.
     *
     * @return The name of the command
     */
    fun getName(): String

    /**
     * Gets the description of the command.
     *
     * @return The description of the command
     */
    fun getDescription(): String

    /**
     * Gets the help text for the command.
     *
     * @return The help text for the command
     */
    fun getHelp(): String

    /**
     * Gets the usage examples for the command.
     *
     * @return A list of usage examples for the command
     */
    fun getExamples(): List<String>

    /**
     * Gets the parent command, if any.
     *
     * @return The parent command, or null if this is a top-level command
     */
    fun getParent(): Command?

    /**
     * Gets the subcommands of this command.
     *
     * @return A list of subcommands
     */
    fun getSubcommands(): List<Command>

    /**
     * Gets the aliases for this command.
     *
     * @return A list of aliases for this command
     */
    fun getAliases(): List<String>

    /**
     * Gets the plugin ID that provides this command.
     *
     * @return The plugin ID, or null if this is a core command
     */
    fun getPluginId(): String?

    /**
     * Checks if this command is hidden from help listings.
     *
     * @return true if the command is hidden, false otherwise
     */
    fun isHidden(): Boolean

    /**
     * Checks if this command is deprecated.
     *
     * @return true if the command is deprecated, false otherwise
     */
    fun isDeprecated(): Boolean

    /**
     * Gets the deprecation message, if any.
     *
     * @return The deprecation message, or null if the command is not deprecated
     */
    fun getDeprecationMessage(): String?

    /**
     * Executes the command with the given arguments.
     *
     * @param args The command arguments
     * @param context The command execution context
     * @return The result of the command execution
     * @throws IllegalArgumentException if the arguments are invalid
     * @throws RuntimeException if execution fails
     */
    suspend fun execute(args: List<String>, context: CommandContext): CommandResult
}