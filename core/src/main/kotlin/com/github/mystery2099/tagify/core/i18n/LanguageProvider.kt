package com.github.mystery2099.tagify.core.i18n

import org.pf4j.ExtensionPoint
import java.nio.file.Path
import java.util.Locale

/**
 * Interface for providing language strings in the Tagify system.
 *
 * The LanguageProvider is responsible for loading, parsing, and accessing language strings
 * from various sources, such as TOML files or resource bundles. It supports multiple languages
 * and fallback mechanisms.
 */
interface LanguageProvider : ExtensionPoint {
    /**
     * Gets the name of the language format.
     *
     * @return The name of the language format (e.g., "TOML", "Properties")
     */
    fun getFormatName(): String

    /**
     * Gets the file extensions supported by this provider.
     *
     * @return A list of file extensions (without the dot) supported by this provider
     */
    fun getSupportedExtensions(): List<String>

    /**
     * Initializes the language provider with the given base directory.
     *
     * @param langDirectory The directory containing language files
     * @throws IllegalArgumentException if the directory is invalid
     * @throws RuntimeException if initialization fails
     */
    suspend fun initialize(langDirectory: Path)

    /**
     * Loads a language file.
     *
     * @param path The path to the language file
     * @param locale The locale of the language file
     * @param isUserOverride Whether this file is a user override
     * @param pluginId The ID of the plugin providing this file, or null for core files
     * @throws IllegalArgumentException if the file is invalid
     * @throws RuntimeException if loading fails
     */
    suspend fun loadLanguage(
        path: Path,
        locale: Locale,
        isUserOverride: Boolean = false,
        pluginId: String? = null
    )

    /**
     * Loads a language file with the given name from the language directory.
     *
     * @param name The name of the language file (without extension)
     * @param locale The locale of the language file
     * @param isUserOverride Whether this file is a user override
     * @param pluginId The ID of the plugin providing this file, or null for core files
     * @throws IllegalArgumentException if the file is invalid
     * @throws RuntimeException if loading fails
     */
    suspend fun loadLanguage(
        name: String,
        locale: Locale,
        isUserOverride: Boolean = false,
        pluginId: String? = null
    )

    /**
     * Gets a language string.
     *
     * @param key The key of the language string
     * @param locale The locale to get the string for
     * @param args Arguments to format the string with
     * @return The language string, or the key if the string is not found
     * @throws IllegalArgumentException if the key is invalid
     * @throws RuntimeException if getting the string fails
     */
    fun getString(key: String, locale: Locale, vararg args: Any): String

    /**
     * Gets a language string using the current locale.
     *
     * @param key The key of the language string
     * @param args Arguments to format the string with
     * @return The language string, or the key if the string is not found
     * @throws IllegalArgumentException if the key is invalid
     * @throws RuntimeException if getting the string fails
     */
    fun getString(key: String, vararg args: Any): String

    /**
     * Gets all language keys.
     *
     * @param locale The locale to get the keys for
     * @return A list of all language keys for the specified locale
     * @throws RuntimeException if getting the keys fails
     */
    fun getLanguageKeys(locale: Locale): List<String>

    /**
     * Checks if a language key exists.
     *
     * @param key The key to check
     * @param locale The locale to check the key for
     * @return true if the key exists, false otherwise
     * @throws IllegalArgumentException if the key is invalid
     * @throws RuntimeException if checking fails
     */
    fun hasString(key: String, locale: Locale): Boolean

    /**
     * Sets a language string.
     *
     * @param key The key of the language string
     * @param value The language string
     * @param locale The locale to set the string for
     * @param isUserOverride Whether this is a user override
     * @throws IllegalArgumentException if the key or value is invalid
     * @throws RuntimeException if setting the string fails
     */
    suspend fun setString(key: String, value: String, locale: Locale, isUserOverride: Boolean = true)

    /**
     * Saves the current language strings to a file.
     *
     * @param path The path to save the language strings to
     * @param locale The locale to save the strings for
     * @throws RuntimeException if saving fails
     */
    suspend fun saveLanguage(path: Path, locale: Locale)

    /**
     * Saves the current language strings to a file with the given name in the language directory.
     *
     * @param name The name of the language file (without extension)
     * @param locale The locale to save the strings for
     * @throws RuntimeException if saving fails
     */
    suspend fun saveLanguage(name: String, locale: Locale)

    /**
     * Reloads the language strings from all loaded files.
     *
     * @throws RuntimeException if reloading fails
     */
    suspend fun reloadLanguages()

    /**
     * Gets the current locale.
     *
     * @return The current locale
     */
    fun getCurrentLocale(): Locale

    /**
     * Sets the current locale.
     *
     * @param locale The locale to set as current
     */
    fun setCurrentLocale(locale: Locale)

    /**
     * Gets all available locales.
     *
     * @return A list of all available locales
     */
    fun getAvailableLocales(): List<Locale>

    /**
     * Gets the directory containing language files.
     *
     * @return The directory containing language files
     */
    fun getLanguageDirectory(): Path
}