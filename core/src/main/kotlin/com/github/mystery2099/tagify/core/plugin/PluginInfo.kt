package com.github.mystery2099.tagify.core.plugin

import kotlinx.serialization.Serializable
import org.pf4j.PluginDescriptor
import org.pf4j.PluginState

/**
 * Represents information about a plugin in the Tagify system.
 *
 * This class stores metadata about plugins, including their ID, version, description,
 * provider, dependencies, and state. It can be created from a PF4J PluginDescriptor.
 *
 * @property id Unique identifier for the plugin
 * @property version Version string of the plugin
 * @property name Human-readable name of the plugin
 * @property description Description of the plugin's functionality
 * @property provider Name of the plugin provider/author
 * @property license License information for the plugin
 * @property requires List of plugin IDs that this plugin depends on
 * @property state Current state of the plugin (CREATED, RESOLVED, STARTED, STOPPED, FAILED)
 * @property capabilities List of capabilities provided by this plugin
 */
@Serializable
data class PluginInfo(
    val id: String,
    val version: String,
    val name: String,
    val description: String = "",
    val provider: String = "",
    val license: String = "",
    val requires: List<String> = emptyList(),
    val state: String = PluginState.CREATED.toString(),
    val capabilities: List<String> = emptyList()
) {
    /**
     * Checks if the plugin is in a started state.
     *
     * @return true if the plugin state is STARTED, false otherwise
     */
    fun isStarted(): Boolean = state == PluginState.STARTED.toString()

    /**
     * Checks if the plugin is in a failed state.
     *
     * @return true if the plugin state is FAILED, false otherwise
     */
    fun isFailed(): Boolean = state == PluginState.FAILED.toString()

    /**
     * Checks if the plugin provides a specific capability.
     *
     * @param capability The capability to check for
     * @return true if the plugin provides the specified capability, false otherwise
     */
    fun hasCapability(capability: String): Boolean = capabilities.contains(capability)

    companion object {
        /**
         * Creates a PluginInfo instance from a PF4J PluginDescriptor and state.
         *
         * @param descriptor The PF4J PluginDescriptor
         * @param state The current state of the plugin
         * @param capabilities List of capabilities provided by the plugin
         * @return A new PluginInfo instance
         */
        fun fromDescriptor(
            descriptor: PluginDescriptor,
            state: PluginState,
            capabilities: List<String> = emptyList()
        ): PluginInfo = PluginInfo(
            id = descriptor.pluginId,
            version = descriptor.version,
            name = descriptor.pluginDescription,
            description = descriptor.pluginDescription,
            provider = descriptor.provider,
            license = descriptor.license,
            requires = descriptor.dependencies.map { it.pluginId },
            state = state.toString(),
            capabilities = capabilities
        )
    }
}