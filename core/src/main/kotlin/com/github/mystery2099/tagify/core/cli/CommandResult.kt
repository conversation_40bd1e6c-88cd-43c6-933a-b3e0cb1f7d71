package com.github.mystery2099.tagify.core.cli

/**
 * Represents the result of a command execution in the Tagify system.
 *
 * CommandResult provides information about the success or failure of a command execution,
 * including an exit code, a message, and optional data.
 *
 * @property exitCode The exit code of the command execution (0 for success, non-zero for failure)
 * @property message A message describing the result of the command execution
 * @property data Optional data returned by the command
 */
data class CommandResult(
    val exitCode: Int,
    val message: String,
    val data: Any? = null
) {
    /**
     * Checks if the command execution was successful.
     *
     * @return true if the exit code is 0, false otherwise
     */
    fun isSuccess(): Boolean = exitCode == 0

    companion object {
        /**
         * Creates a successful command result.
         *
         * @param message A message describing the successful result
         * @param data Optional data returned by the command
         * @return A CommandResult with exit code 0
         */
        fun success(message: String = "Command executed successfully", data: Any? = null): CommandResult =
            CommandResult(0, message, data)

        /**
         * Creates a failed command result.
         *
         * @param message A message describing the failure
         * @param exitCode The exit code of the command execution (defaults to 1)
         * @param data Optional data related to the failure
         * @return A CommandResult with a non-zero exit code
         */
        fun failure(message: String, exitCode: Int = 1, data: Any? = null): CommandResult =
            CommandResult(exitCode, message, data)

        /**
         * Creates a command result for invalid arguments.
         *
         * @param message A message describing the invalid arguments
         * @return A CommandResult with exit code 2
         */
        fun invalidArguments(message: String): CommandResult =
            CommandResult(2, message)

        /**
         * Creates a command result for a command not found error.
         *
         * @param commandName The name of the command that was not found
         * @return A CommandResult with exit code 3
         */
        fun commandNotFound(commandName: String): CommandResult =
            CommandResult(3, "Command not found: $commandName")

        /**
         * Creates a command result for a permission denied error.
         *
         * @param message A message describing the permission denied error
         * @return A CommandResult with exit code 4
         */
        fun permissionDenied(message: String): CommandResult =
            CommandResult(4, message)

        /**
         * Creates a command result for an internal error.
         *
         * @param message A message describing the internal error
         * @param data Optional data related to the error (e.g., an exception)
         * @return A CommandResult with exit code 5
         */
        fun internalError(message: String, data: Any? = null): CommandResult =
            CommandResult(5, message, data)
    }
}