plugins {
    // Apply the shared build logic from a convention plugin.
    id("buildsrc.convention.kotlin-jvm")
    // Apply Kotlin Serialization plugin from `gradle/libs.versions.toml`.
    alias(libs.plugins.kotlinPluginSerialization)
}

dependencies {
    // Apply the kotlinx bundle of dependencies from the version catalog
    implementation(libs.bundles.kotlinxEcosystem)
    
    // Apply the tagify core dependencies
    implementation(libs.bundles.tagifyCore)
    
    // Test dependencies
    testImplementation(kotlin("test"))
}